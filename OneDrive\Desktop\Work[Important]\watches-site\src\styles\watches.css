/* Carousel Container */
.carousel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

/* Main Carousel Section */
.carousel-main {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  width: 100%;
}

/* Image Container */
.carousel-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  position: relative;
}

/* Main Image */
.carousel-main-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  transition: opacity 0.3s ease-in-out;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Fade Effects */
.carousel-main-image.fade-in {
  opacity: 1;
}

.carousel-main-image.fade-out {
  opacity: 0;
}

/* Arrow Buttons */
.carousel-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 50px;
  background: transparent;
  border:  solid #000000;
  border-radius: 0px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #000000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.carousel-arrow:hover {
  background: transparent;
  border-color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.carousel-arrow:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.carousel-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.carousel-arrow:disabled:hover {
  background: #ffffff;
  border-color: #e0e0e0;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Arrow SVG Icons */
.carousel-arrow svg {
  width: 24px;
  height: 24px;
}

/* Thumbnail Navigation */
.carousel-thumbnails {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Thumbnail Buttons */
.carousel-thumbnail {
  width: 80px;
  height: 80px;
  border: 3px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9f9f900;
  padding: 4px;
}

.carousel-thumbnail:hover {
  border-color: #ccc;
  transform: scale(1.05);
}

.carousel-thumbnail.active {
  border-color: #333;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.2);
}

.carousel-thumbnail:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.carousel-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .carousel-main {
    gap: 10px;
  }

  .carousel-arrow {
    width: 40px;
    height: 40px;
  }

  .carousel-arrow svg {
    width: 20px;
    height: 20px;
  }

  .carousel-thumbnail {
    width: 60px;
    height: 60px;
  }

  .carousel-thumbnails {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    padding: 10px;
  }

  .carousel-main {
    flex-direction: column;
    gap: 15px;
  }

  .carousel-arrow {
    position: absolute;
    z-index: 10;
  }

  .carousel-arrow-left {
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .carousel-arrow-right {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .carousel-arrow:hover {
    transform: translateY(-50%) scale(1.1);
  }
}