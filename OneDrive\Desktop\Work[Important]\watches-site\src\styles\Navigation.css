/* Glassmorphism Pill Navigation */
.pill-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.nav-pill {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 7px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.sub-pill {
  position: absolute;
  top: 7px;
  left: 7px;
  bottom: 7px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 40px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: 1;
  width: calc((100% - 14px) / 4);
}

.nav-item {
  position: relative;
  background: none;
  border: none;
  padding: 12px 30px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 40px;
  z-index: 2;
  white-space: nowrap;
  min-width: 80px;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.nav-item.active {
  color: rgba(0, 0, 0, 0.8);
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .nav-pill {
    padding: 6px;
  }
  
  .nav-item {
    padding: 10px 16px;
    font-size: 12px;
    min-width: 60px;
  }
  
  .sub-pill {
    top: 6px;
    left: 6px;
    bottom: 6px;
    width: calc((100% - 12px) / 4);
  }
}

@media (max-width: 480px) {
  .pill-navigation {
    padding: 15px;
    top: 15px;
  }
  
  .nav-pill {
    padding: 4px;
  }
  
  .nav-item {
    padding: 8px 12px;
    font-size: 11px;
    min-width: 50px;
  }
  
  .sub-pill {
    top: 4px;
    left: 4px;
    bottom: 4px;
    width: calc((100% - 8px) / 4);
  }
}

/* Enhanced glassmorphism for different backgrounds */
.nav-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50px;
  pointer-events: none;
}

/* Subtle animation on load */
.pill-navigation {
  animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Focus states for accessibility */
.nav-item:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-item:focus:not(:focus-visible) {
  outline: none;
}
