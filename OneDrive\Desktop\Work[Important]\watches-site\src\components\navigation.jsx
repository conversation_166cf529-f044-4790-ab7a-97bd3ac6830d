import { useState } from 'react'
import '../styles/Navigation.css'

const Navigation = () => {
  const [activeIndex, setActiveIndex] = useState(0)

  const navItems = [
    { label: 'Home', id: 'home' },
    { label: 'Watches', id: 'watches' },
    { label: 'About', id: 'about' },
    { label: 'Contact', id: 'contact' }
  ]

  const handleNavClick = (index) => {
    setActiveIndex(index)
  }

  return (
    <nav className="pill-navigation">
      <div className="nav-pill">
        <div
          className="sub-pill"
          style={{
            transform: `translateX(${activeIndex * 100}%)`
          }}
        />
        {navItems.map((item, index) => (
          <button
            key={item.id}
            className={`nav-item ${index === activeIndex ? 'active' : ''}`}
            onClick={() => handleNavClick(index)}
          >
            {item.label}
          </button>
        ))}
      </div>
    </nav>
  )
}

export default Navigation